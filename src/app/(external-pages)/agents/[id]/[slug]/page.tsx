import React from "react";
import { Metadata } from "next";
import { fetchAgentById } from "../../lib/api";
import { notFound } from "next/navigation";
import SingleAgentClient from "./components/single-agent-client";

interface PageProps {
  params: {
    id: string;
    slug: string;
  };
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const agent = await fetchAgentById(params.id);
  
  if (!agent) {
    return {
      title: "Agent Not Found | Telex",
      description: "The requested agent could not be found.",
    };
  }

  return {
    title: `${agent.app_name} | Telex AI Agents`,
    description: agent.app_description || `Learn more about ${agent.app_name} and how it can help your business.`,
    keywords: `${agent.app_name}, AI agent, automation, ${agent.category}`,
    openGraph: {
      title: `${agent.app_name} | Telex AI Agents`,
      description: agent.app_description || `Learn more about ${agent.app_name} and how it can help your business.`,
      type: "website",
      images: agent.app_logo ? [{ url: agent.app_logo }] : [],
    },
  };
}

export default async function SingleAgentPage({ params }: PageProps) {
  const agent = await fetchAgentById(params.id);
  
  if (!agent) {
    notFound();
  }

  return (
    <div className="bg-white min-h-screen">
      <SingleAgentClient agent={agent} />
    </div>
  );
}
