"use client";
import React, { useState } from "react";
import { Agent } from "../../../page";
import AgentDescription from "./agent-description";
import AgentHeader from "./agent-header";
import AgentTabs from "./agent-tabs";

interface SingleAgentClientProps {
  agent: Agent;
}

const SingleAgentClient = ({ agent }: SingleAgentClientProps) => {

  console.log('agent', agent);
  const [activeTab, setActiveTab] = useState("Description");

  const tabs = [
    "Description", 
    "Pricing",
    "API",
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case "Description":
        return <AgentDescription agent={agent} />;
      case "Pricing":
        return <div className="p-6">Pricing information coming soon...</div>;
      case "API":
        return <div className="p-6">API documentation coming soon...</div>;
      default:
        return <AgentDescription agent={agent} />;
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-0 py-8">
      <AgentHeader agent={agent} />
      <div className="flex flex-col lg:flex-row gap-8 mt-8">
        <div className="flex-1">
          <AgentTabs 
            tabs={tabs} 
            activeTab={activeTab} 
            onTabChange={setActiveTab} 
          />
          <div className="mt-6">
            {renderTabContent()}
          </div>
        </div>
        <div className="lg:w-80">
         
        </div>
      </div>
    </div>
  );
};

export default SingleAgentClient;
